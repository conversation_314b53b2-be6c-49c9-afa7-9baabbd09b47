
import type Konva from "konva";
import { XAXIS_HEIGHT } from "../commons/constants";
import { useAppContext, useLayoutContext } from "../contexts/contexts";
import { useChartContext } from "../contexts/contexts";

import { TCoord, TRange, TSignal } from "../types/commonTypes";
import { useSignal } from "./useSignal";

export const usePaneScale = () => {
    console.debug('use pane scale')
    const { timeRange } = useAppContext()
    const { valueRange, xRange, yRange } = useChartContext()

    return useScale({
        xRange,
        yRange,
        valueRange,
        timeRange
    })
}

export const useYAxisScale = () => {
    console.debug('use y axis scale')
    const { valueRange, yAxisWidth, height } = useChartContext()

    return useScale({
        xRange: useSignal({ min: 0, max: yAxisWidth }),
        yRange: useSignal({ min: 0, max: height }),
        valueRange,
        timeRange: useSignal({ min: 0, max: yAxisWidth })
    })
}

export const useXAxisScale = () => {
    console.debug('use x axis scale')
    const { layoutInfo } = useLayoutContext()
    const { timeRange } = useAppContext()
    return useScale({
        xRange: useSignal({ min: 0, max: layoutInfo?.xAxisWidth ?? 0 }),
        yRange: useSignal({ min: 0, max: XAXIS_HEIGHT }),
        valueRange: useSignal({ min: 0, max: XAXIS_HEIGHT }),
        timeRange
    })
}

export const useDrawingScale = (groupRef: React.RefObject<Konva.Group | null>) => {
    console.debug('use drawing scale')
    const toRelativeX = (x: number) => {
        if (!groupRef.current) return x;
        const groupPos = groupRef.current.position()
        return x - groupPos.x;
    }

    const toRelativeY = (y: number) => {
        if (!groupRef.current) return y;
        const groupPos = groupRef.current.position()
        return y - groupPos.y;
    }

    const toAbsoluteY = (y: number) => {
        if (!groupRef.current) return y;
        const groupPos = groupRef.current.position()
        return y + groupPos.y;
    }

    const toAbsoluteCoord = (coord: TCoord) => {
        const groupPos = groupRef.current?.position() || { x: 0, y: 0 };
        return { x: coord.x + groupPos.x, y: coord.y + groupPos.y };
    }
    const toRelativeCoord = (coord: TCoord) => {
        const groupPos = groupRef.current?.position() || { x: 0, y: 0 };
        return { x: coord.x - groupPos.x, y: coord.y - groupPos.y };
    }

    return {
        toRelativeX,
        toRelativeY,
        toAbsoluteY,
        toAbsoluteCoord,
        toRelativeCoord
    }
}

export const useScale = (props: { xRange: TSignal<TRange>, yRange: TSignal<TRange>, valueRange: TSignal<TRange>, timeRange: TSignal<TRange> }) => {
    console.debug('use scale')
    const { xRange, yRange, valueRange, timeRange } = props
    xRange.use()
    yRange.use()
    valueRange.use()
    timeRange.use()

    const v2y = (d: number) => {
        const valueSpan = valueRange.value.max - valueRange.value.min
        if (!valueRange.value || !xRange.value || !yRange.value || !valueSpan) return NaN;
        if (valueSpan <= 0) return NaN;
        // Invert Y-axis (0 at top, height at bottom)
        return yRange.value.max - ((d - valueRange.value.min) / valueSpan * yRange.value.max);
    }

    const y2v = (c: number) => {
        const valueSpan = valueRange.value.max - valueRange.value.min
        if (!valueRange.value || !xRange.value || !yRange.value) return NaN;
        if (valueSpan <= 0) return NaN;
        // Invert Y-axis (0 at top, height at bottom)
        return valueRange.value.min + (valueSpan * (yRange.value.max - c) / yRange.value.max);
    }

    const t2x = (d: number) => {
        const timeSpan = timeRange.value.max - timeRange.value.min
        if (!timeRange.value || !xRange.value) return NaN;
        if (timeSpan <= 0) return 0;
        return ((d - timeRange.value.min) / timeSpan) * xRange.value.max;
    }

    const x2t = (c: number) => {
        const timeSpan = timeRange.value.max - timeRange.value.min
        if (!timeRange.value || !xRange.value) return NaN;
        if (timeSpan <= 0) return 0;
        return timeRange.value.min + (timeSpan * c / xRange.value.max);
    }

    const deltaX2t = (deltaX: number) => {
        const timeSpan = timeRange.value.max - timeRange.value.min
        if (!timeRange.value || !xRange.value) return NaN;
        if (timeSpan <= 0) return 0;
        return timeSpan * deltaX / xRange.value.max;
    }

    const deltaY2v = (deltaY: number) => {
        const valueSpan = valueRange.value.max - valueRange.value.min
        if (!valueRange.value || !yRange.value) return NaN;
        if (valueSpan <= 0) return NaN;
        return valueSpan * deltaY / yRange.value.max;
    }

    const deltaT2x = (deltaT: number) => {
        const timeSpan = timeRange.value.max - timeRange.value.min
        if (!timeRange.value || !xRange.value) return NaN;
        if (timeSpan <= 0) return NaN;
        return (deltaT / timeSpan) * xRange.value.max;
    }

    const deltaV2y = (deltaV: number) => {
        const valueSpan = valueRange.value.max - valueRange.value.min
        if (!valueRange.value || !yRange.value) return NaN;
        if (valueSpan <= 0) return NaN;
        return (deltaV / valueSpan) * yRange.value.max;
    }

    return {
        v2y,
        y2v,
        t2x,
        x2t,
        deltaX2t,
        deltaY2v,
        deltaT2x,
        deltaV2y,
    }
}